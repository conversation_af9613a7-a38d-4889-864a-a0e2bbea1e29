{"articles": [{"id": 1, "title": "井冈山精神的时代价值", "content": "井冈山精神是中国共产党在井冈山创建革命根据地、开辟中国革命道路过程中培育和形成的伟大精神。", "author": "红色传承研究组", "date": "2025-05-20", "category": "精神传承", "tags": ["井冈山精神", "红色文化", "时代价值"]}, {"id": 2, "title": "jQuery选择器深度解析", "content": "jQuery选择器是jQuery库的核心功能之一，它让我们能够轻松地选择和操作DOM元素。", "author": "前端技术团队", "date": "2025-05-19", "category": "技术分享", "tags": ["j<PERSON><PERSON><PERSON>", "选择器", "DOM操作"]}, {"id": 3, "title": "JavaScript数组方法全解", "content": "JavaScript数组提供了丰富的方法来处理数据，包括map、filter、reduce等高阶函数。", "author": "JavaScript专家", "date": "2025-05-18", "category": "技术分享", "tags": ["JavaScript", "数组", "高阶函数"]}, {"id": 4, "title": "Vue.js组件化开发实践", "content": "组件化是Vue.js的核心思想，通过组件化可以提高代码的复用性和维护性。", "author": "Vue.js开发者", "date": "2025-05-17", "category": "技术分享", "tags": ["Vue.js", "组件化", "前端开发"]}, {"id": 5, "title": "传承红色基因，学习现代技术", "content": "在学习现代Web技术的同时，我们要传承井冈山精神，将红色基因融入到技术学习中。", "author": "教育工作者", "date": "2025-05-16", "category": "精神传承", "tags": ["红色基因", "技术学习", "教育实践"]}], "studyPlans": [{"week": 1, "topic": "JavaScript基础语法", "tasks": ["变量和数据类型", "函数和作用域", "对象和数组", "条件语句和循环"], "spirit_connection": "学习JavaScript基础体现了'实事求是'的精神，扎实掌握基础知识。"}, {"week": 2, "topic": "DOM操作和事件处理", "tasks": ["DOM选择和操作", "事件监听和处理", "表单验证", "动态内容更新"], "spirit_connection": "DOM操作需要'艰苦奋斗'的精神，反复练习才能熟练掌握。"}, {"week": 3, "topic": "jQuery库的使用", "tasks": ["jQuery选择器", "jQuery DOM操作", "jQuery事件处理", "jQuery动画效果"], "spirit_connection": "学习jQuery体现了'敢闯新路'的精神，用新工具提高开发效率。"}, {"week": 4, "topic": "Ajax和异步编程", "tasks": ["Ajax基础概念", "XMLHttpRequest使用", "Promise和async/await", "数据交互实践"], "spirit_connection": "Ajax技术需要'依靠群众'的精神，前后端协作完成数据交互。"}, {"week": 5, "topic": "项目实践和总结", "tasks": ["井冈山精神主题网站", "代码优化和重构", "性能优化", "项目部署"], "spirit_connection": "项目实践体现了'勇于胜利'的精神，将所学知识应用到实际项目中。"}], "spiritValues": [{"name": "坚定信念", "description": "在学习技术的道路上保持坚定的信念，不畏困难，持续前进。", "techApplication": "在遇到复杂的编程问题时，保持信心，相信通过努力一定能够解决。"}, {"name": "艰苦奋斗", "description": "学习编程需要大量的练习和思考，要有艰苦奋斗的精神。", "techApplication": "每天坚持编码练习，不断提升自己的技术水平。"}, {"name": "实事求是", "description": "在技术学习中要实事求是，不弄虚作假，扎实掌握每一个知识点。", "techApplication": "写代码时要严谨认真，测试充分，确保代码质量。"}, {"name": "敢闯新路", "description": "勇于尝试新技术，探索新的解决方案。", "techApplication": "积极学习新的前端框架和工具，不断更新技术栈。"}, {"name": "依靠群众", "description": "在学习过程中要善于与他人交流合作，共同进步。", "techApplication": "参与开源项目，与其他开发者交流学习，分享技术经验。"}, {"name": "勇于胜利", "description": "面对技术挑战时要有勇于胜利的决心和信心。", "techApplication": "在项目开发中遇到困难时，要有攻坚克难的勇气，最终完成项目目标。"}]}