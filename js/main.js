/**
 * 井冈山精神 - jQuery学习平台主要JavaScript文件
 * @description 实现页面交互功能，包含DOM操作、事件处理、动画效果等
 * <AUTHOR>
 * @date 2025-05-26 09:57:39
 */

// 全局变量和数据对象
const AppData = {
    // 技术栈数据数组
    techStack: [
        {
            name: 'JavaScript',
            description: '现代Web开发的核心语言，用于创建动态交互效果',
            features: ['ES6+语法', '异步编程', 'DOM操作', '事件处理'],
            color: '#f7df1e'
        },
        {
            name: 'jQuery',
            description: '快速、简洁的JavaScript库，简化HTML文档操作',
            features: ['选择器', 'DOM操作', '事件处理', '动画效果'],
            color: '#0769ad'
        },
        {
            name: 'Vue.js',
            description: '渐进式JavaScript框架，用于构建用户界面',
            features: ['响应式数据', '组件化', '虚拟DOM', '生态丰富'],
            color: '#4fc08d'
        },
        {
            name: 'Java',
            description: '企业级开发语言，跨平台、面向对象',
            features: ['跨平台', '面向对象', '企业级', '生态完善'],
            color: '#ed8b00'
        }
    ],

    // 学习计划数据
    studyPlans: [
        { date: '2025-05-26', task: 'JavaScript基础语法复习', status: 'completed' },
        { date: '2025-05-27', task: 'jQuery选择器深入学习', status: 'pending' },
        { date: '2025-05-28', task: 'DOM操作实践项目', status: 'pending' },
        { date: '2025-05-29', task: 'Ajax技术应用开发', status: 'pending' },
        { date: '2025-05-30', task: '井冈山精神主题整合', status: 'pending' }
    ],

    // 井冈山精神相关内容
    spiritContent: {
        title: '井冈山精神',
        description: '坚定信念、艰苦奋斗、实事求是、敢闯新路、依靠群众、勇于胜利',
        values: ['坚定信念', '艰苦奋斗', '实事求是', '敢闯新路', '依靠群众', '勇于胜利']
    }
};

// 工具函数对象
const Utils = {
    /**
     * 格式化日期
     * @param {Date} date 日期对象
     * @return {string} 格式化后的日期字符串
     */
    formatDate: function(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        const weekday = weekdays[date.getDay()];

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${weekday}`;
    },

    /**
     * 生成随机颜色
     * @return {string} 十六进制颜色值
     */
    getRandomColor: function() {
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
        return colors[Math.floor(Math.random() * colors.length)];
    },

    /**
     * 防抖函数
     * @param {Function} func 要执行的函数
     * @param {number} delay 延迟时间
     * @return {Function} 防抖后的函数
     */
    debounce: function(func, delay) {
        let timeoutId;
        return function(...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }
};

// 主应用对象
const App = {
    /**
     * 初始化应用
     * @description 页面加载完成后的初始化操作
     */
    init: function() {
        this.updateDateTime();
        this.bindEvents();
        this.initAnimations();
        this.setupAjaxDemo();
        this.initCarousel();
        this.createParticleEffect();
        this.initSpiritQuiz();

        // 显示欢迎消息
        this.showWelcomeMessage();

        console.log('井冈山精神学习平台初始化完成');
    },

    /**
     * 更新日期时间显示
     * @description 实时更新页面顶部的日期时间
     */
    updateDateTime: function() {
        const updateTime = () => {
            const now = new Date();
            $('#currentDate').text(Utils.formatDate(now));
        };

        updateTime();
        setInterval(updateTime, 1000);
    },

    /**
     * 绑定事件处理器
     * @description 为页面元素绑定各种事件
     */
    bindEvents: function() {
        // 导航链接点击事件
        $('.nav-link').on('click', this.handleNavClick.bind(this));

        // 推荐项点击事件
        $('.rec-item-card').on('click', this.handleRecItemClick.bind(this));

        // 轮播图控制事件
        $('#carouselPrev').on('click', this.prevSlide.bind(this));
        $('#carouselNext').on('click', this.nextSlide.bind(this));
        $('.indicator').on('click', this.goToSlide.bind(this));

        // 详情项悬停事件
        $('.detail-item').on('mouseenter', this.handleDetailHover.bind(this));
        $('.detail-item').on('mouseleave', this.handleDetailLeave.bind(this));

        // 模态框关闭事件
        $('#closeModal').on('click', this.closeModal.bind(this));
        $('.modal').on('click', function(e) {
            if (e.target === this) {
                App.closeModal();
            }
        });

        // 键盘事件
        $(document).on('keydown', this.handleKeyDown.bind(this));

        // 窗口滚动事件
        $(window).on('scroll', Utils.debounce(this.handleScroll.bind(this), 100));
    },

    /**
     * 处理导航点击事件
     * @param {Event} e 事件对象
     */
    handleNavClick: function(e) {
        e.preventDefault();
        const $target = $(e.target);
        const section = $target.data('section');

        // 更新活动状态
        $('.nav-link').removeClass('active');
        $target.addClass('active');

        // 根据不同section显示不同内容
        this.showSectionContent(section);

        // 添加点击动画效果
        $target.animate({
            fontSize: '1.1em'
        }, 200).animate({
            fontSize: '1em'
        }, 200);
    },

    /**
     * 显示不同section的内容
     * @param {string} section 要显示的section
     */
    showSectionContent: function(section) {
        const contentMap = {
            'home': '欢迎来到井冈山精神学习平台首页',
            'tech': '技术分享：JavaScript、jQuery、Vue.js等前端技术',
            'articles': '文章收藏：精选技术文章和学习资源',
            'spirit': '井冈山精神：' + AppData.spiritContent.description,
            'about': '关于我们：致力于传承井冈山精神，推广现代Web技术',
            'contact': '联系我们：江西省吉安市青原区井冈山大学',
            'register': '用户注册：加入我们的学习社区'
        };

        // 使用jQuery动画显示内容
        $('.jquery-subtitle').fadeOut(300, function() {
            $(this).text(contentMap[section] || '页面内容').fadeIn(300);
        });
    },

    /**
     * 处理推荐项点击事件
     * @param {Event} e 事件对象
     */
    handleRecItemClick: function(e) {
        const $item = $(e.currentTarget);
        const content = $item.data('content');

        // 添加点击动画
        $item.addClass('clicked');
        setTimeout(() => $item.removeClass('clicked'), 300);

        // 显示内容详情模态框
        this.showContentModal(content);
    },

    /**
     * 初始化轮播图
     */
    initCarousel: function() {
        this.currentSlide = 0;
        this.totalSlides = $('.carousel-item').length;

        // 自动播放
        this.startAutoPlay();
    },

    /**
     * 下一张幻灯片
     */
    nextSlide: function() {
        this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
        this.updateCarousel();
    },

    /**
     * 上一张幻灯片
     */
    prevSlide: function() {
        this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
        this.updateCarousel();
    },

    /**
     * 跳转到指定幻灯片
     * @param {Event} e 事件对象
     */
    goToSlide: function(e) {
        this.currentSlide = parseInt($(e.target).data('slide'));
        this.updateCarousel();
    },

    /**
     * 更新轮播图显示
     */
    updateCarousel: function() {
        $('.carousel-item').removeClass('active');
        $('.carousel-item').eq(this.currentSlide).addClass('active');

        $('.indicator').removeClass('active');
        $('.indicator').eq(this.currentSlide).addClass('active');
    },

    /**
     * 开始自动播放
     */
    startAutoPlay: function() {
        setInterval(() => {
            this.nextSlide();
        }, 5000); // 每5秒切换一次
    },

    /**
     * 显示内容详情模态框
     * @param {string} contentType 内容类型
     */
    showContentModal: function(contentType) {
        const contentMap = {
            'js-tutorial': {
                title: 'JavaScript 教程',
                description: '现代Web开发的核心语言，用于创建动态交互效果',
                content: 'JavaScript是一种高级的、解释型的编程语言，是Web开发的核心技术之一。它支持面向对象、命令式和函数式编程风格。',
                features: ['ES6+语法', '异步编程', 'DOM操作', '事件处理']
            },
            'jquery-tutorial': {
                title: 'jQuery 教程',
                description: '快速、简洁的JavaScript库，简化HTML文档操作',
                content: 'jQuery是一个快速、小巧且功能丰富的JavaScript库。它通过易于使用的API简化了HTML文档遍历和操作、事件处理、动画和Ajax。',
                features: ['选择器', 'DOM操作', '事件处理', '动画效果']
            },
            'vue-tutorial': {
                title: 'Vue.js 教程',
                description: '渐进式JavaScript框架，用于构建用户界面',
                content: 'Vue.js是一套用于构建用户界面的渐进式框架。与其它大型框架不同的是，Vue被设计为可以自底向上逐层应用。',
                features: ['响应式数据', '组件化', '虚拟DOM', '生态丰富']
            },
            'java-tutorial': {
                title: 'Java 教程',
                description: '企业级开发语言，跨平台、面向对象',
                content: 'Java是一种广泛使用的计算机编程语言，拥有跨平台、面向对象、泛型编程的特性，广泛应用于企业级Web应用开发。',
                features: ['跨平台', '面向对象', '企业级', '生态完善']
            },
            'jquery-advanced': {
                title: 'jQuery 高级教程',
                description: 'jQuery高级技巧和最佳实践',
                content: '深入学习jQuery的高级特性，包括插件开发、性能优化、与现代框架的集成等内容。',
                features: ['插件开发', '性能优化', '高级选择器', '自定义动画']
            }
        };

        const content = contentMap[contentType];
        if (content) {
            $('#modalTitle').text(content.title);

            const featuresHtml = content.features ?
                `<div class="tech-features">
                    <h4>主要特性：</h4>
                    <ul>
                        ${content.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                </div>` : '';

            const modalContent = `
                <div class="content-detail">
                    <p class="content-description">${content.description}</p>
                    <div class="content-body">
                        <p>${content.content}</p>
                    </div>
                    ${featuresHtml}
                    <div class="spirit-connection">
                        <h4>学习精神：</h4>
                        <p>秉承井冈山精神中"实事求是闯新路"的理念，在技术学习中勇于探索，敢于创新。</p>
                    </div>
                </div>
            `;

            $('#modalBody').html(modalContent);
            $('#techModal').fadeIn(300);
        }
    },

    /**
     * 关闭模态框
     */
    closeModal: function() {
        $('#techModal').fadeOut(300);
    },

    /**
     * 处理Logo点击事件
     */
    handleLogoClick: function() {
        const $logo = $('#jqueryLogo');

        // 创建复杂动画序列
        $logo.animate({
            transform: 'scale(1.2) rotate(360deg)'
        }, 1000, function() {
            $(this).animate({
                transform: 'scale(1) rotate(0deg)'
            }, 500);
        });

        // 同时触发特效
        this.createSparkleEffect($logo);
    },

    /**
     * 创建闪烁特效
     * @param {jQuery} $element 目标元素
     */
    createSparkleEffect: function($element) {
        const $container = $element.parent();

        for (let i = 0; i < 10; i++) {
            const $sparkle = $('<div class="sparkle">✨</div>');
            $sparkle.css({
                position: 'absolute',
                left: Math.random() * $container.width() + 'px',
                top: Math.random() * $container.height() + 'px',
                fontSize: Math.random() * 20 + 10 + 'px',
                opacity: 1,
                pointerEvents: 'none',
                zIndex: 1000
            });

            $container.append($sparkle);

            $sparkle.animate({
                top: '-=50px',
                opacity: 0
            }, 2000, function() {
                $(this).remove();
            });
        }
    },

    /**
     * 处理详情项悬停事件
     * @param {Event} e 事件对象
     */
    handleDetailHover: function(e) {
        const $item = $(e.currentTarget);
        $item.css('background-color', Utils.getRandomColor() + '20');
    },

    /**
     * 处理详情项离开事件
     * @param {Event} e 事件对象
     */
    handleDetailLeave: function(e) {
        const $item = $(e.currentTarget);
        $item.css('background-color', '');
    },

    /**
     * 处理键盘事件
     * @param {Event} e 事件对象
     */
    handleKeyDown: function(e) {
        // ESC键关闭模态框
        if (e.keyCode === 27) {
            this.closeModal();
        }

        // 空格键触发Logo动画
        if (e.keyCode === 32 && e.target.tagName !== 'INPUT') {
            e.preventDefault();
            this.handleLogoClick();
        }
    },

    /**
     * 处理滚动事件
     */
    handleScroll: function() {
        const scrollTop = $(window).scrollTop();
        const $header = $('.header');

        if (scrollTop > 100) {
            $header.addClass('scrolled');
        } else {
            $header.removeClass('scrolled');
        }
    },

    /**
     * 初始化动画效果
     */
    initAnimations: function() {
        // 页面加载动画
        $('.rec-item').each(function(index) {
            $(this).delay(index * 100).animate({
                opacity: 1,
                transform: 'translateY(0)'
            }, 500);
        });

        // 特性标签动画
        $('.feature-tag').each(function(index) {
            $(this).delay(index * 200).fadeIn(500);
        });
    },



    /**
     * 设置Ajax演示
     */
    setupAjaxDemo: function() {
        // Ajax演示功能已禁用，不再动态加载技术文章
        console.log('Ajax演示功能已禁用');
    },

    /**
     * 模拟加载文章数据 - 已禁用
     * @description 此功能已被禁用，不再动态添加技术文章到今日详情
     */
    loadArticlesData: function() {
        // 功能已禁用，不再添加技术相关文章
        console.log('文章数据加载功能已禁用');
    },

    /**
     * 显示欢迎消息
     */
    showWelcomeMessage: function() {
        const messages = [
            '欢迎来到井冈山精神学习平台！',
            '让我们一起传承红色精神，学习现代技术！',
            '坚定信念，敢闯新路，在技术的道路上勇敢前行！'
        ];

        let messageIndex = 0;
        const showNextMessage = () => {
            if (messageIndex < messages.length) {
                $('.jquery-subtitle').fadeOut(300, function() {
                    $(this).text(messages[messageIndex]).fadeIn(300);
                    messageIndex++;
                    setTimeout(showNextMessage, 3000);
                });
            }
        };

        setTimeout(showNextMessage, 1000);
    },

    /**
     * 创建粒子效果
     */
    createParticleEffect: function() {
        const $particleContainer = $('<div class="particles"></div>');
        $('.jquery-section').append($particleContainer);

        setInterval(() => {
            const $particle = $('<div class="particle"></div>');
            $particle.css({
                left: Math.random() * 100 + '%',
                animationDelay: Math.random() * 2 + 's',
                animationDuration: (Math.random() * 3 + 3) + 's'
            });

            $particleContainer.append($particle);

            setTimeout(() => {
                $particle.remove();
            }, 6000);
        }, 500);
    },



    /**
     * 井冈山精神知识问答
     */
    initSpiritQuiz: function() {
        const questions = [
            {
                question: '井冈山精神的核心内容包括哪些？',
                options: ['坚定信念', '艰苦奋斗', '实事求是', '以上都是'],
                correct: 3,
                explanation: '井冈山精神包含坚定信念、艰苦奋斗、实事求是、敢闯新路、依靠群众、勇于胜利等内容。'
            },
            {
                question: '在学习编程时，哪种行为体现了"实事求是"的精神？',
                options: ['复制粘贴代码', '认真测试代码', '快速完成任务', '忽略错误'],
                correct: 1,
                explanation: '认真测试代码，确保代码质量，体现了实事求是的精神。'
            }
        ];

        let currentQuestion = 0;
        let score = 0;

        const showQuiz = () => {
            if (currentQuestion < questions.length) {
                const q = questions[currentQuestion];
                const $quiz = $(`
                    <div class="quiz-modal modal" style="display: block;">
                        <div class="modal-content">
                            <h3>井冈山精神知识问答 (${currentQuestion + 1}/${questions.length})</h3>
                            <p class="quiz-question">${q.question}</p>
                            <div class="quiz-options">
                                ${q.options.map((option, index) =>
                                    `<button class="quiz-option" data-index="${index}">${option}</button>`
                                ).join('')}
                            </div>
                            <div class="quiz-explanation" style="display: none;">
                                <p>${q.explanation}</p>
                                <button class="quiz-next">下一题</button>
                            </div>
                        </div>
                    </div>
                `);

                $('body').append($quiz);

                $('.quiz-option').on('click', function() {
                    const selectedIndex = parseInt($(this).data('index'));
                    const isCorrect = selectedIndex === q.correct;

                    $('.quiz-option').prop('disabled', true);
                    $(this).addClass(isCorrect ? 'correct' : 'incorrect');
                    $('.quiz-option').eq(q.correct).addClass('correct');

                    if (isCorrect) score++;

                    $('.quiz-explanation').fadeIn();
                });

                $('.quiz-next').on('click', function() {
                    $quiz.remove();
                    currentQuestion++;
                    setTimeout(showQuiz, 500);
                });
            } else {
                // 显示最终结果
                const $result = $(`
                    <div class="quiz-modal modal" style="display: block;">
                        <div class="modal-content">
                            <h3>问答完成！</h3>
                            <p>您的得分：${score}/${questions.length}</p>
                            <p>${score === questions.length ? '优秀！您很好地理解了井冈山精神！' : '继续学习，加深对井冈山精神的理解！'}</p>
                            <button class="quiz-close">关闭</button>
                        </div>
                    </div>
                `);

                $('body').append($result);
                $('.quiz-close').on('click', () => $result.remove());
            }
        };

        // 添加问答按钮
        const $quizButton = $('<button class="quiz-start-btn">开始井冈山精神问答</button>');
        $quizButton.on('click', showQuiz);
        $('.sidebar-right').append($quizButton);
    },


};

// 页面加载完成后初始化应用
$(document).ready(function() {
    App.init();

    // 添加一些CSS动画类
    $('<style>').text(`
        .clicked { transform: scale(0.95) !important; }
        .sparkle { animation: sparkle 2s ease-out forwards; }
        @keyframes sparkle {
            0% { transform: translateY(0) scale(1); opacity: 1; }
            100% { transform: translateY(-50px) scale(0); opacity: 0; }
        }
        .scrolled { box-shadow: 0 4px 20px rgba(0,0,0,0.2) !important; }
        .plan-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .plan-item.completed { opacity: 0.7; }
        .plan-date { font-size: 0.8em; color: #666; }
        .plan-task { flex: 1; margin: 0 10px; }
        .plan-status { font-size: 1.2em; }
        .article-item { background-color: #f0f8ff; }
        .tech-detail { line-height: 1.8; }
        .tech-features { margin: 10px 0; padding-left: 20px; }
        .spirit-connection {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff3cd;
            border-radius: 5px;
        }
    `).appendTo('head');
});
